import { UserRole } from "@prisma/client"
import { getServerSession } from "next-auth"
import { authOptions } from "./auth"
import { redirect } from "next/navigation"

export async function getCurrentUser() {
  const session = await getServerSession(authOptions)
  return session?.user
}

export async function requireAuth() {
  const user = await getCurrentUser()
  if (!user) {
    redirect("/auth/signin")
  }
  return user
}

export async function requireRole(allowedRoles: UserRole[]) {
  const user = await requireAuth()
  if (!allowedRoles.includes(user.role)) {
    redirect("/unauthorized")
  }
  return user
}

export async function requireFaculty() {
  return requireRole([UserRole.FACULTY])
}

export async function requireAdmin() {
  return requireRole([UserRole.COLLEGE_ADMIN, UserRole.SYS_ADMIN])
}

export async function requireSysAdmin() {
  return requireRole([UserRole.SYS_ADMIN])
}

export function hasRole(userRole: UserRole, allowedRoles: UserRole[]): boolean {
  return allowedRoles.includes(userRole)
}

export function isFaculty(userRole: UserRole): boolean {
  return userRole === UserRole.FACULTY
}

export function isAdmin(userRole: UserRole): boolean {
  return userRole === UserRole.COLLEGE_ADMIN || userRole === UserRole.SYS_ADMIN
}

export function isSysAdmin(userRole: UserRole): boolean {
  return userRole === UserRole.SYS_ADMIN
}

export function isStudent(userRole: UserRole): boolean {
  return userRole === UserRole.STUDENT
}
