import { Card, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { requireFaculty } from "@/lib/auth-utils"
import { prisma } from "@/lib/prisma"
import { Save, User } from "lucide-react"

async function getFacultyProfile(userId: string) {
  return await prisma.facultyProfile.findUnique({
    where: { userId },
    include: {
      user: {
        include: {
          profile: true
        }
      },
      department: true
    }
  })
}

async function getDepartments() {
  return await prisma.department.findMany({
    select: {
      id: true,
      name: true
    },
    orderBy: {
      name: 'asc'
    }
  })
}

export default async function FacultyProfilePage() {
  const user = await requireFaculty()
  const facultyProfile = await getFacultyProfile(user.id)
  const departments = await getDepartments()

  if (!facultyProfile) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle>Faculty Profile Not Found</CardTitle>
            <CardDescription>
              Your faculty profile hasn't been set up yet. Please contact the administrator.
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">My Profile</h1>
        <p className="text-gray-600">Manage your faculty profile information</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <User className="w-5 h-5 mr-2" />
              Basic Information
            </CardTitle>
            <CardDescription>
              Your personal and contact information
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="firstName">First Name</Label>
                <Input 
                  id="firstName" 
                  defaultValue={facultyProfile.user.profile?.firstName || ''} 
                />
              </div>
              <div>
                <Label htmlFor="lastName">Last Name</Label>
                <Input 
                  id="lastName" 
                  defaultValue={facultyProfile.user.profile?.lastName || ''} 
                />
              </div>
            </div>
            
            <div>
              <Label htmlFor="email">Email</Label>
              <Input 
                id="email" 
                type="email" 
                defaultValue={facultyProfile.user.email} 
                disabled
                className="bg-gray-50"
              />
              <p className="text-xs text-gray-500 mt-1">Email cannot be changed</p>
            </div>

            <div>
              <Label htmlFor="phone">Phone</Label>
              <Input 
                id="phone" 
                defaultValue={facultyProfile.user.profile?.phone || ''} 
              />
            </div>

            <div>
              <Label htmlFor="title">Title</Label>
              <Input 
                id="title" 
                defaultValue={facultyProfile.title} 
                placeholder="e.g., Dr., Prof., Assistant Professor"
              />
            </div>

            <div>
              <Label htmlFor="department">Department</Label>
              <select 
                id="department" 
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                defaultValue={facultyProfile.departmentId}
              >
                {departments.map((dept) => (
                  <option key={dept.id} value={dept.id}>
                    {dept.name}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <Label htmlFor="office">Office Location</Label>
              <Input 
                id="office" 
                defaultValue={facultyProfile.officeLocation || ''} 
                placeholder="e.g., Room 123, Science Building"
              />
            </div>
          </CardContent>
        </Card>

        {/* Professional Information */}
        <Card>
          <CardHeader>
            <CardTitle>Professional Information</CardTitle>
            <CardDescription>
              Your academic and professional details
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="website">Personal Website</Label>
              <Input 
                id="website" 
                type="url" 
                defaultValue={facultyProfile.websiteUrl || ''} 
                placeholder="https://your-website.com"
              />
            </div>

            <div>
              <Label htmlFor="scholar">Google Scholar ID</Label>
              <Input 
                id="scholar" 
                defaultValue={facultyProfile.scholarId || ''} 
                placeholder="Your Google Scholar profile ID"
              />
            </div>

            <div>
              <Label htmlFor="bio">Biography</Label>
              <Textarea 
                id="bio" 
                rows={6}
                defaultValue={facultyProfile.bio || ''} 
                placeholder="Write a brief biography about yourself, your research interests, and background..."
              />
              <p className="text-xs text-gray-500 mt-1">
                This will be displayed on your public faculty profile
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Action Buttons */}
      <div className="flex justify-end space-x-4">
        <Button variant="outline">
          Cancel
        </Button>
        <Button>
          <Save className="w-4 h-4 mr-2" />
          Save Changes
        </Button>
      </div>

      {/* Quick Stats */}
      <Card>
        <CardHeader>
          <CardTitle>Profile Completion</CardTitle>
          <CardDescription>
            Complete your profile to improve visibility
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm">Basic Information</span>
              <span className="text-sm font-medium text-green-600">Complete</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">Biography</span>
              <span className="text-sm font-medium text-yellow-600">
                {facultyProfile.bio ? 'Complete' : 'Incomplete'}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">Contact Information</span>
              <span className="text-sm font-medium text-green-600">Complete</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">Professional Links</span>
              <span className="text-sm font-medium text-yellow-600">
                {facultyProfile.websiteUrl || facultyProfile.scholarId ? 'Complete' : 'Incomplete'}
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Help Section */}
      <Card>
        <CardHeader>
          <CardTitle>Need Help?</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-sm text-gray-600 space-y-2">
            <p>• Your profile information will be displayed on the public faculty directory</p>
            <p>• Make sure your biography is professional and highlights your expertise</p>
            <p>• Adding your Google Scholar ID helps showcase your research impact</p>
            <p>• Contact the administrator if you need to change your email or department</p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
