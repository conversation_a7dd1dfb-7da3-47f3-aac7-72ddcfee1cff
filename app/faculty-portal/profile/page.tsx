import { Card, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { requireFaculty } from "@/lib/auth-utils"
import { prisma } from "@/lib/prisma"
import { Save, User } from "lucide-react"
import { FacultyProfileForm } from "@/components/faculty/FacultyProfileForm"

async function getFacultyProfile(userId: string) {
  return await prisma.facultyProfile.findUnique({
    where: { userId },
    include: {
      user: {
        include: {
          profile: true
        }
      },
      department: true
    }
  })
}

async function getDepartments() {
  return await prisma.department.findMany({
    select: {
      id: true,
      name: true
    },
    orderBy: {
      name: 'asc'
    }
  })
}

export default async function FacultyProfilePage() {
  const user = await requireFaculty()
  const facultyProfile = await getFacultyProfile(user.id)
  const departments = await getDepartments()

  if (!facultyProfile) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle>Faculty Profile Not Found</CardTitle>
            <CardDescription>
              Your faculty profile hasn't been set up yet. Please contact the administrator.
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">My Profile</h1>
        <p className="text-gray-600">Manage your faculty profile information</p>
      </div>

      {/* Interactive Profile Form */}
      <FacultyProfileForm
        profile={{
          id: facultyProfile.id,
          bio: facultyProfile.bio,
          title: facultyProfile.title,
          office: facultyProfile.officeLocation,
          officeHours: null, // TODO: Add office hours field to schema
          website: facultyProfile.websiteUrl,
          phone: facultyProfile.user.profile?.phone,
          researchInterests: null // TODO: Add research interests field to schema
        }}
        userEmail={facultyProfile.user.email}
        userName={facultyProfile.user.name || ''}
      />

      {/* Quick Stats */}
      <Card>
        <CardHeader>
          <CardTitle>Profile Completion</CardTitle>
          <CardDescription>
            Complete your profile to improve visibility
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className="text-sm">Basic Information</span>
              <span className="text-sm font-medium text-green-600">Complete</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">Biography</span>
              <span className="text-sm font-medium text-yellow-600">
                {facultyProfile.bio ? 'Complete' : 'Incomplete'}
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">Contact Information</span>
              <span className="text-sm font-medium text-green-600">Complete</span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">Professional Links</span>
              <span className="text-sm font-medium text-yellow-600">
                {facultyProfile.websiteUrl || facultyProfile.scholarId ? 'Complete' : 'Incomplete'}
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Help Section */}
      <Card>
        <CardHeader>
          <CardTitle>Need Help?</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-sm text-gray-600 space-y-2">
            <p>• Your profile information will be displayed on the public faculty directory</p>
            <p>• Make sure your biography is professional and highlights your expertise</p>
            <p>• Adding your Google Scholar ID helps showcase your research impact</p>
            <p>• Contact the administrator if you need to change your email or department</p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
