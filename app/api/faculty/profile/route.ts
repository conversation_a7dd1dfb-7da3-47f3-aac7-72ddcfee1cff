import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

// Validation schema for profile updates
const profileUpdateSchema = z.object({
  bio: z.string().optional(),
  title: z.string().optional(),
  office: z.string().optional(),
  officeHours: z.string().optional(),
  website: z.string().url().optional().or(z.literal('')),
  phone: z.string().optional(),
  researchInterests: z.string().optional(),
})

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || session.user.role !== 'FACULTY') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const facultyProfile = await prisma.facultyProfile.findUnique({
      where: { userId: session.user.id },
      include: {
        department: true,
        publications: {
          orderBy: { year: 'desc' }
        },
        researchAreas: true,
        education: {
          orderBy: { year: 'desc' }
        },
        timeline: {
          orderBy: { year: 'desc' }
        }
      }
    })

    if (!facultyProfile) {
      return NextResponse.json({ error: 'Faculty profile not found' }, { status: 404 })
    }

    return NextResponse.json({ profile: facultyProfile })
  } catch (error) {
    console.error('Error fetching faculty profile:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || session.user.role !== 'FACULTY') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = profileUpdateSchema.parse(body)

    // Update faculty profile
    const updatedProfile = await prisma.facultyProfile.update({
      where: { userId: session.user.id },
      data: {
        ...validatedData,
        updatedAt: new Date()
      },
      include: {
        department: true
      }
    })

    return NextResponse.json({ 
      message: 'Profile updated successfully',
      profile: updatedProfile 
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json({ 
        error: 'Validation error',
        details: error.errors 
      }, { status: 400 })
    }

    console.error('Error updating faculty profile:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
