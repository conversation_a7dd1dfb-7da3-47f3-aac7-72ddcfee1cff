'use client'

import Link from "next/link"
import { ArrowRight, BookOpen, Building, Leaf, Lock, Menu, Users, Image as ImageIcon, MapPin, BarChart3, GraduationCap, <PERSON>rkles, Clock } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Sheet, <PERSON><PERSON><PERSON>ontent, SheetTrigger } from "@/components/ui/sheet"
import { useParallaxEffect } from "@/components/parallax-effect"
import { PageTransition } from "@/components/ui/page-transition"
import { SkipLink } from "@/components/ui/skip-link"
import { LazyImage } from "@/components/ui/lazy-image"
import { LoadingSpinner } from "@/components/ui/loading-spinner"
import { StickyCTA } from "@/components/ui/sticky-cta"
import Header from "@/components/layout/Header"
import Footer from "@/components/layout/Footer"
import HeroSection from "@/components/landing/HeroSection"
import ProgramsOverviewSection from "@/components/landing/ProgramsOverviewSection"
import DepartmentSection from "@/components/landing/DepartmentSection"
import FacultySection from "@/components/landing/FacultySection"
import CampusLifeSection from "@/components/landing/CampusLifeSection"
import CallToActionSection from "@/components/landing/CallToActionSection"
import PostsSection from "@/components/landing/PostsSection"
import { Badge } from "@/components/ui/badge"

// Data for Computer Science Section
const csFeaturedProgram = {
  name: "B.Tech in Cybersecurity",
  slug: "btech-cybersecurity",
  title: "B.Tech in Cybersecurity",
  details: "Our comprehensive Cybersecurity program equips students with the skills to protect digital assets and infrastructure from evolving threats. Learn from industry experts and gain hands-on experience in our state-of-the-art security labs.",
  points: [
    { text: "Network Security & Ethical Hacking", icon: <Lock className="h-4 w-4 text-crimson" /> },
    { text: "Digital Forensics & Incident Response", icon: <Lock className="h-4 w-4 text-crimson" /> },
    { text: "Security Operations & Threat Intelligence", icon: <Lock className="h-4 w-4 text-crimson" /> },
  ],
};

const csProgramCards = [
  {
    name: "Computer Science",
    slug: "btech-cybersecurity",
    description: "Core fundamentals and advanced topics",
    details: "Our Computer Science program covers algorithms, data structures, software engineering, and more. Students develop strong problem-solving skills applicable across industries.",
  },
  {
    name: "Artificial Intelligence",
    slug: "btech-ai-ml",
    description: "Machine learning and AI applications",
    details: "Study machine learning, neural networks, and AI applications. Work on real-world projects and prepare for careers in this rapidly growing field.",
  },
  {
    name: "Data Science",
    slug: "btech-data-science",
    description: "Analytics and data-driven decision making",
    details: "Learn to extract insights from complex datasets. Develop skills in statistical analysis, data visualization, and predictive modeling.",
  },
];

// Data for Agriculture Section
const agriculturePrograms = [
  { name: "B.Sc. in Sustainable Agriculture", slug: "bsc-sustainable-agriculture", icon: <Leaf className="h-4 w-4 text-green-600" /> },
  { name: "B.Sc. in Climate Science", slug: "bsc-climate-science", icon: <Leaf className="h-4 w-4 text-green-600" /> },
  { name: "B.Sc. in Environmental Management", slug: "bsc-environmental-management", icon: <Leaf className="h-4 w-4 text-green-600" /> },
];

export default function CollegePage() {
  // const scrolled = useScrollHeader()

  // Initialize parallax effect for campus life section
  useParallaxEffect()

  return (
    <PageTransition transitionType="fade" duration={300}>
      <div className="flex min-h-screen flex-col">
        <SkipLink />
        <Header />
        <main id="main-content" className="flex-1" tabIndex={-1}>
          <HeroSection />
          
          {/* Enhanced Tools Section */}
          <section className="py-16 relative overflow-hidden bg-gradient-to-b from-muted/30 to-background">
            <div className="absolute inset-0 overflow-hidden z-0">
              <div className="absolute right-1/4 top-1/3 w-72 h-72 bg-blue-500/5 rounded-full blur-3xl" />
              <div className="absolute left-1/4 top-2/3 w-80 h-80 bg-crimson/5 rounded-full blur-3xl" />
            </div>
            
            <div className="px-4 md:px-6 relative z-10">
              <div className="mb-12 text-center">
                <Badge className="mb-4" variant="outline">Tools & Resources</Badge>
                <h2 className="text-3xl md:text-4xl font-bold mb-3">Explore Your Academic Journey</h2>
                <p className="text-muted-foreground max-w-2xl mx-auto">
                  Discover the right program for your goals and visualize your path from application to graduation.
                </p>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-6xl mx-auto">
                {/* Program Comparison Tool Card */}
                <div className="bg-background rounded-2xl shadow-md border border-border/40 p-6 hover:shadow-lg transition-all duration-300 group relative overflow-hidden">
                  <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-500 to-purple-500"></div>
                  <div className="absolute -right-16 -top-16 w-32 h-32 bg-blue-500/10 rounded-full blur-2xl group-hover:bg-blue-500/15 transition-all duration-500"></div>
                  
                  <div className="flex items-center gap-4 mb-4">
                    <div className="h-14 w-14 rounded-xl bg-gradient-to-br from-blue-500 to-purple-500 p-[2px] group-hover:scale-105 transition-all duration-300">
                      <div className="w-full h-full bg-background rounded-[10px] flex items-center justify-center">
                        <BarChart3 className="h-6 w-6 text-blue-500" />
                      </div>
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold group-hover:text-blue-500 transition-colors">Program Comparison Tool</h3>
                      <Badge variant="outline" className="mt-1 bg-blue-50 text-blue-700 border-blue-200">
                        <Sparkles className="h-3 w-3 mr-1" /> Interactive
                      </Badge>
                    </div>
                  </div>
                  
                  <p className="mb-5 text-muted-foreground">
                    Compare different programs side-by-side to find the perfect fit for your academic goals and interests.
                  </p>
                  
                  <div className="flex flex-wrap gap-3 mb-5">
                    <Badge variant="secondary" className="font-normal">Compare Curriculums</Badge>
                    <Badge variant="secondary" className="font-normal">Requirements</Badge>
                    <Badge variant="secondary" className="font-normal">Career Paths</Badge>
                  </div>
                  
                  <Link href="/programs/compare" className="block">
                    <Button className="w-full group-hover:bg-blue-500 transition-colors">
                      Compare Programs <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                    </Button>
                  </Link>
                </div>
                
                {/* Student Journey Card */}
                <div className="bg-background rounded-2xl shadow-md border border-border/40 p-6 hover:shadow-lg transition-all duration-300 group relative overflow-hidden">
                  <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-crimson to-amber-500"></div>
                  <div className="absolute -left-16 -top-16 w-32 h-32 bg-crimson/10 rounded-full blur-2xl group-hover:bg-crimson/15 transition-all duration-500"></div>
                  
                  <div className="flex items-center gap-4 mb-4">
                    <div className="h-14 w-14 rounded-xl bg-gradient-to-br from-crimson to-amber-500 p-[2px] group-hover:scale-105 transition-all duration-300">
                      <div className="w-full h-full bg-background rounded-[10px] flex items-center justify-center">
                        <GraduationCap className="h-6 w-6 text-crimson" />
                      </div>
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold group-hover:text-crimson transition-colors">Student Journey</h3>
                      <Badge variant="outline" className="mt-1 bg-crimson/10 text-crimson border-crimson/20">
                        <Clock className="h-3 w-3 mr-1" /> Timeline
                      </Badge>
                    </div>
                  </div>
                  
                  <p className="mb-5 text-muted-foreground">
                    Explore the student experience from application to graduation with our interactive timeline and resources.
                  </p>
                  
                  <div className="flex flex-wrap gap-3 mb-5">
                    <Badge variant="secondary" className="font-normal">Application</Badge>
                    <Badge variant="secondary" className="font-normal">Coursework</Badge>
                    <Badge variant="secondary" className="font-normal">Graduation</Badge>
                  </div>
                  
                  <Link href="/student-journey" className="block">
                    <Button className="w-full group-hover:bg-crimson transition-colors">
                      View Student Journey <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                    </Button>
                  </Link>
                </div>
              </div>
            </div>
          </section>
          
          <ProgramsOverviewSection />
          
          <FacultySection />
          <CampusLifeSection />
          <PostsSection />
          <CallToActionSection id="apply" />
        </main>
        <Footer />
        <StickyCTA />
      </div>
    </PageTransition>
  )
}
