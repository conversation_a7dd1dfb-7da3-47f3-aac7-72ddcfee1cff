"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { useToast } from "@/hooks/use-toast"
import { Save, Loader2 } from "lucide-react"

interface FacultyProfile {
  id: string
  bio?: string | null
  title?: string | null
  office?: string | null
  officeHours?: string | null
  website?: string | null
  phone?: string | null
  researchInterests?: string | null
}

interface FacultyProfileFormProps {
  profile: FacultyProfile
  userEmail: string
  userName: string
}

export function FacultyProfileForm({ profile, userEmail, userName }: FacultyProfileFormProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [formData, setFormData] = useState({
    bio: profile.bio || "",
    title: profile.title || "",
    office: profile.office || "",
    officeHours: profile.officeHours || "",
    website: profile.website || "",
    phone: profile.phone || "",
    researchInterests: profile.researchInterests || ""
  })
  
  const { toast } = useToast()
  const router = useRouter()

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      const response = await fetch('/api/faculty/profile', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.error || 'Failed to update profile')
      }

      const result = await response.json()
      
      toast({
        title: "Profile Updated",
        description: "Your profile has been successfully updated.",
      })

      // Refresh the page to show updated data
      router.refresh()
    } catch (error) {
      console.error('Error updating profile:', error)
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to update profile. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Basic Information */}
      <Card>
        <CardHeader>
          <CardTitle>Basic Information</CardTitle>
          <CardDescription>
            Update your basic profile information
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="name">Full Name</Label>
              <Input 
                id="name" 
                value={userName} 
                disabled 
                className="bg-gray-50"
              />
              <p className="text-xs text-gray-500 mt-1">Contact admin to change name</p>
            </div>
            <div>
              <Label htmlFor="email">Email Address</Label>
              <Input 
                id="email" 
                value={userEmail} 
                disabled 
                className="bg-gray-50"
              />
              <p className="text-xs text-gray-500 mt-1">Contact admin to change email</p>
            </div>
          </div>

          <div>
            <Label htmlFor="title">Title/Position</Label>
            <Input 
              id="title" 
              value={formData.title}
              onChange={(e) => handleInputChange('title', e.target.value)}
              placeholder="e.g., Associate Professor, Department Chair"
            />
          </div>

          <div>
            <Label htmlFor="bio">Bio</Label>
            <Textarea 
              id="bio" 
              value={formData.bio}
              onChange={(e) => handleInputChange('bio', e.target.value)}
              rows={4}
              placeholder="Write a brief bio about yourself, your background, and interests..."
            />
          </div>
        </CardContent>
      </Card>

      {/* Contact Information */}
      <Card>
        <CardHeader>
          <CardTitle>Contact Information</CardTitle>
          <CardDescription>
            Update your contact details and office information
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="phone">Phone Number</Label>
              <Input 
                id="phone" 
                value={formData.phone}
                onChange={(e) => handleInputChange('phone', e.target.value)}
                placeholder="(*************"
              />
            </div>
            <div>
              <Label htmlFor="office">Office Location</Label>
              <Input 
                id="office" 
                value={formData.office}
                onChange={(e) => handleInputChange('office', e.target.value)}
                placeholder="e.g., Science Building, Room 305"
              />
            </div>
          </div>

          <div>
            <Label htmlFor="website">Website URL</Label>
            <Input 
              id="website" 
              type="url"
              value={formData.website}
              onChange={(e) => handleInputChange('website', e.target.value)}
              placeholder="https://your-website.com"
            />
          </div>

          <div>
            <Label htmlFor="officeHours">Office Hours</Label>
            <Textarea 
              id="officeHours" 
              value={formData.officeHours}
              onChange={(e) => handleInputChange('officeHours', e.target.value)}
              rows={3}
              placeholder="e.g., Mondays and Wednesdays: 2:00 PM - 4:00 PM, or by appointment"
            />
          </div>
        </CardContent>
      </Card>

      {/* Research Information */}
      <Card>
        <CardHeader>
          <CardTitle>Research Information</CardTitle>
          <CardDescription>
            Describe your research interests and focus areas
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="researchInterests">Research Interests</Label>
            <Textarea 
              id="researchInterests" 
              value={formData.researchInterests}
              onChange={(e) => handleInputChange('researchInterests', e.target.value)}
              rows={4}
              placeholder="Describe your research interests, methodologies, and current projects..."
            />
          </div>
        </CardContent>
      </Card>

      {/* Save Button */}
      <div className="flex justify-end">
        <Button type="submit" disabled={isLoading}>
          {isLoading ? (
            <>
              <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              Saving...
            </>
          ) : (
            <>
              <Save className="w-4 h-4 mr-2" />
              Save Changes
            </>
          )}
        </Button>
      </div>
    </form>
  )
}
