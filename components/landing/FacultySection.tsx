import FacultyCard from "./FacultyCard";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";
import Link from "next/link";

const facultyData = [
  {
    id: "sarah-johnson",
    imageUrl: "/placeholder.jpg", // Replace with actual image paths
    altText: "Dr. <PERSON>",
    name: "Dr. <PERSON>",
    title: "Dean, Computer Science",
    bio: "Ph.D. in Computer Science from MIT with 15+ years of experience in AI research and education.",
  },
  {
    id: "micha<PERSON>-chen",
    imageUrl: "/placeholder.jpg",
    altText: "Prof. <PERSON>",
    name: "Prof. <PERSON>",
    title: "Chair, Business School",
    bio: "MBA from Harvard Business School with extensive experience in corporate leadership and entrepreneurship.",
  },
  {
    id: "emily-rod<PERSON><PERSON><PERSON>",
    imageUrl: "/placeholder.jpg",
    altText: "Dr. <PERSON>",
    name: "Dr. <PERSON>",
    title: "Professor, Agriculture",
    bio: "Ph.D. in Environmental Science with research focus on sustainable farming practices and climate adaptation.",
  },
  {
    id: "jam<PERSON>-wils<PERSON>",
    imageUrl: "/placeholder.jpg",
    altText: "Dr. <PERSON>",
    name: "Dr. <PERSON>",
    title: "Director, Education",
    bio: "Ed.D. in Educational Leadership with 20+ years of experience in curriculum development and teacher training.",
  },
];

export default function FacultySection() {
  return (
    <section id="faculty" className="w-full py-20 md:py-28 lg:py-32 bg-background relative overflow-hidden">
      {/* Abstract geometric shapes */}
      <div className="absolute inset-0 overflow-hidden opacity-10">
        <div className="absolute top-1/3 left-1/4 w-72 h-72 bg-crimson rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/4 right-1/4 w-64 h-64 bg-gold/70 rounded-full blur-3xl"></div>
      </div>

      <div className="px-4 md:px-6 relative">
        <div className="flex flex-col items-center justify-center space-y-4 text-center mb-16">
          <div className="inline-flex items-center px-3 py-1 rounded-full bg-crimson/10 text-crimson text-sm font-medium">
            Our Faculty
          </div>
          <div className="space-y-4">
            <h2 className="heading-lg">
              Meet Our Distinguished Faculty
            </h2>
            <p className="text-muted-foreground text-lg leading-relaxed">
              Our dedicated professors and staff bring years of industry experience and academic excellence to provide you with the best education.
            </p>
          </div>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 md:gap-8">
          {facultyData.map((faculty) => (
            <FacultyCard
              key={faculty.id}
              id={faculty.id}
              imageUrl={faculty.imageUrl}
              altText={faculty.altText}
              name={faculty.name}
              title={faculty.title}
              bio={faculty.bio}
            />
          ))}
        </div>

        <div className="flex justify-center mt-12">
          <Link href="/faculty">
            <Button className="shadow-md transition-all hover:shadow-lg hover:scale-105 duration-300">
              Meet All Faculty
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          </Link>
        </div>
      </div>
    </section>
  );
} 